# Compiled Output
dist
tmp
/out-tsc

# Project Dependencies
.git
.gitignore
node_modules

# Docker
compose*.yml
Dockerfile

# IDEs and Editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vs/*
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Miscellaneous
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
.editorconfig
.eslint*

# Generated Files
.nx
.swc
fly.toml
stats.html
tools/compose/*
tools/scripts/*

# Environment Variables
*.env*
!.env.example

# Lingui Compiled Messages
apps/client/src/locales/_build/
apps/client/src/locales/*/messages.mjs

# Documentation and Scripts
*.md
docs/
PROJECT_SETUP.md
CHANGELOG.md
CODE_OF_CONDUCT.md
CONTRIBUTING.md
LICENSE.md
README.md
SECURITY.md

# Batch and Shell Scripts
*.bat
*.sh
quick-start.bat
daily-start.bat
daily-stop.bat
production-start.bat
production-stop.bat
production-status.bat
production-cleanup.bat

# Test Files
*.test.*
*.spec.*
**/*.test.*
**/*.spec.*
test/
tests/
__tests__/

# Development Tools
.ncurc.json
crowdin.yml
