{
  "root": true,
  "ignorePatterns": ["**/*"],
  "plugins": ["@nx"],
  "overrides": [
    {
      "files": ["*.ts", "*.tsx", "*.js", "*.jsx"],
      "extends": ["plugin:prettier/recommended"],
      "plugins": ["simple-import-sort", "unused-imports"],
      "rules": {
        // eslint
        "no-console": "error",
        "no-return-await": "off",

        // simple-import-sort
        "simple-import-sort/imports": "error",
        "simple-import-sort/exports": "error",

        // unused-imports
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "warn",
          {
            "vars": "all",
            "varsIgnorePattern": "^_",
            "args": "after-used",
            "argsIgnorePattern": "^_"
          }
        ],

        // nx
        "@nx/enforce-module-boundaries": [
          "error",
          {
            "allowCircularSelfDependency": true,
            "enforceBuildableLibDependency": true,
            "allow": [],
            "depConstraints": [
              {
                "sourceTag": "*",
                "onlyDependOnLibsWithTags": ["*"]
              }
            ]
          }
        ],

        // prettier
        "prettier/prettier": [
          "warn",
          {
            "endOfLine": "auto"
          }
        ]
      }
    },
    {
      "files": ["*.ts", "*.tsx"],
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "sourceType": "module",
        "ecmaVersion": "latest",
        "project": ["tsconfig.*?.json"]
      },
      "extends": [
        "plugin:@nx/typescript",
        "plugin:@typescript-eslint/strict-type-checked",
        "plugin:@typescript-eslint/stylistic-type-checked",
        "plugin:unicorn/recommended"
      ],
      "plugins": ["@typescript-eslint", "unicorn"],
      "rules": {
        // typescript-eslint
        "@typescript-eslint/no-unsafe-call": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/return-await": "off",
        "@typescript-eslint/no-unsafe-return": "off",
        "@typescript-eslint/no-unsafe-argument": "off",
        "@typescript-eslint/no-misused-promises": "off",
        "@typescript-eslint/no-unsafe-assignment": "off",
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/consistent-type-imports": "error",
        "@typescript-eslint/restrict-template-expressions": "off",
        "@typescript-eslint/no-redundant-type-constituents": "off",
        "@typescript-eslint/consistent-type-definitions": ["error", "type"],

        // unicorn
        "unicorn/no-null": "off",
        "unicorn/prevent-abbreviations": "off",
        "unicorn/prefer-string-replace-all": "off",
        "unicorn/prefer-structured-clone": "off"
      }
    },
    {
      "files": ["*.js", "*.jsx"],
      "extends": ["plugin:@nx/javascript"],
      "rules": {
        // eslint
        "no-console": "warn",
        "no-unused-vars": "off"
      }
    }
  ]
}
