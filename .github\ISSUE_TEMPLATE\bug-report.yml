name: 🐞 Bug Report

description: Create a bug report to help improve Reactive Resume

title: "[Bug] <title>"
labels: [bug, v4, needs triage]
assignees: "AmruthPillai"

body:
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search to see if an issue already exists for the bug you encountered.
      options:
        - label: Yes, I have searched the existing issues and none of them match my problem.
          required: true

  - type: dropdown
    id: variant
    attributes:
      label: Product Variant
      description: What variant of Reactive Resume are you using?
      options:
        - Cloud (https://rxresu.me)
        - Self-Hosted
    validations:
      required: true

  - type: textarea
    attributes:
      label: Current Behavior
      description: A concise description of what you're experiencing.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: false

  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Detailed steps to reproduce the behavior, so that it can be easily diagnosed.
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: false

  - type: dropdown
    id: browsers
    attributes:
      label: What browsers are you seeing the problem on?
      multiple: true
      options:
        - Firefox
        - Chrome
        - Safari
        - Microsoft Edge
    validations:
      required: false

  - type: dropdown
    id: template
    attributes:
      label: What template are you using?
      description: Leave blank if the issue applies to all templates, or is not template-specific.
      multiple: false
      options:
        - Azurill
        - Bronzor
        - Chikorita
        - Ditto
        - Gengar
        - Glalie
        - Kakuna
        - Leafish
        - Nosepass
        - Onyx
        - Pikachu
        - Rhyhorn
    validations:
      required: false

  - type: textarea
    attributes:
      label: Anything else?
      description: |
        Links? References? Anything that will give us more context about the issue you are encountering!

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false
