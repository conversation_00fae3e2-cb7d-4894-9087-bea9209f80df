ARG NX_CLOUD_ACCESS_TOKEN

# --- Base Image ---
FROM node:lts-alpine AS base
ARG NX_CLOUD_ACCESS_TOKEN

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN corepack enable

# Install curl for health checks and OpenSSL for Prisma
RUN apk add --no-cache curl openssl1.1-compat

WORKDIR /app

# --- Build Image ---
FROM base AS build
ARG NX_CLOUD_ACCESS_TOKEN

# Copy package files first for better caching
COPY .npmrc package.json pnpm-lock.yaml ./
COPY ./tools/prisma /app/tools/prisma

# Install all dependencies (including dev dependencies for building)
RUN pnpm install --frozen-lockfile

# Copy only necessary source files (exclude docs, scripts, etc.)
COPY apps ./apps
COPY libs ./libs
COPY nx.json ./
COPY tsconfig.base.json ./
COPY tailwind.config.js ./
COPY jest.config.ts ./
COPY jest.preset.js ./
COPY lingui.config.ts ./

ENV NX_CLOUD_ACCESS_TOKEN=$NX_CLOUD_ACCESS_TOKEN
ENV NODE_ENV=production

# Generate Prisma client and build all applications
RUN pnpm run prisma:generate
RUN pnpm run build

# --- Release Image ---
FROM base AS release
ARG NX_CLOUD_ACCESS_TOKEN

RUN apk add --no-cache dumb-init

# Copy package files and install production dependencies
COPY --chown=node:node --from=build /app/.npmrc /app/package.json /app/pnpm-lock.yaml ./
RUN pnpm install --prod --frozen-lockfile

# Copy built applications and Prisma tools
COPY --chown=node:node --from=build /app/dist ./dist
COPY --chown=node:node --from=build /app/tools/prisma ./tools/prisma

# Set proper ownership for node_modules
RUN chown -R node:node /app/node_modules

# Generate Prisma client for production
RUN pnpm run prisma:generate

# Set environment variables
ENV TZ=UTC
ENV PORT=3000
ENV NODE_ENV=production

# Switch to non-root user
USER node

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD [ "dumb-init", "pnpm", "run", "start" ]
