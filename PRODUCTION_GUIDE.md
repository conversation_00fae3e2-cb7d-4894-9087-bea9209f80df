# Reactive Resume 生产环境使用指南

## 🚀 快速开始

### 启动生产环境
```bash
production-start.bat
```

### 停止生产环境
```bash
production-stop.bat
```

### 完全清理（删除所有数据）
```bash
production-cleanup.bat
```

## 🌐 访问地址

- **主应用**: http://localhost:3000
- **MinIO 控制台**: http://localhost:9001 (用户名: minioadmin, 密码: minioadmin)

## 📊 服务管理

### 查看服务状态
```bash
docker compose -f compose.production.yml ps
```

### 查看日志
```bash
# 查看所有服务日志
docker compose -f compose.production.yml logs

# 查看特定服务日志
docker compose -f compose.production.yml logs app
docker compose -f compose.production.yml logs postgres
docker compose -f compose.production.yml logs minio
```

### 重启特定服务
```bash
docker compose -f compose.production.yml restart app
```

## 🔧 高级操作

### 手动构建镜像
```bash
docker compose -f compose.production.yml build --no-cache
```

### 进入容器调试
```bash
docker compose -f compose.production.yml exec app sh
```

### 数据备份
```bash
# 备份数据库
docker compose -f compose.production.yml exec postgres pg_dump -U postgres postgres > backup.sql

# 备份文件存储
docker cp $(docker compose -f compose.production.yml ps -q minio):/data ./minio-backup
```

## 📁 数据持久化

数据存储在以下 Docker volumes 中：
- `reactive-resume_postgres_data` - 数据库数据
- `reactive-resume_minio_data` - 文件存储数据

这些数据在容器重启后会自动恢复。

## ⚠️ 注意事项

1. **首次启动**: 第一次启动需要构建镜像，可能需要 5-10 分钟
2. **端口占用**: 确保端口 3000、9000、9001 未被其他程序占用
3. **Docker 要求**: 需要 Docker Desktop 正在运行
4. **数据安全**: 生产环境请修改默认密码和密钥

## 🛠️ 故障排除

### 服务无法启动
1. 检查 Docker 是否运行
2. 检查端口是否被占用
3. 查看日志: `docker compose -f compose.production.yml logs`

### 应用无法访问
1. 等待服务完全启动（约 1-2 分钟）
2. 检查健康状态: `curl http://localhost:3000/api/health`
3. 重启应用服务: `docker compose -f compose.production.yml restart app`

### 数据丢失
- 数据存储在 Docker volumes 中，除非手动删除否则不会丢失
- 使用 `production-cleanup.bat` 会删除所有数据

## 📞 支持

如遇问题，请检查：
1. Docker Desktop 是否正常运行
2. 系统资源是否充足（至少 4GB RAM）
3. 网络连接是否正常
