# Reactive Resume - 生产环境 Docker 部署

一个完整的、自包含的 Reactive Resume 生产环境，通过 Docker 容器化部署。

## 🚀 一键启动

```bash
# 启动生产环境
production-start.bat

# 访问应用
http://localhost:3000
```

## 📋 管理命令

| 操作 | 命令 |
|------|------|
| 启动服务 | `production-start.bat` |
| 停止服务 | `production-stop.bat` |
| 查看状态 | `production-status.bat` |
| 完全清理 | `production-cleanup.bat` |

## 🌐 访问地址

- **主应用**: http://localhost:3000
- **MinIO 控制台**: http://localhost:9001 (minioadmin/minioadmin)

## 📊 服务组件

- **应用服务**: Reactive Resume 主应用（前端 + 后端）
- **数据库**: PostgreSQL 16
- **文件存储**: MinIO
- **PDF 生成**: Chrome/Chromium 浏览器

## 💾 数据持久化

所有数据自动保存在 Docker volumes 中：
- 数据库数据
- 上传的文件和图片
- 用户生成的简历

## ⚙️ 系统要求

- Docker Desktop
- 4GB+ RAM
- 端口 3000, 9000, 9001 可用

## 🔧 高级操作

```bash
# 查看日志
docker compose -f compose.production.yml logs

# 重启特定服务
docker compose -f compose.production.yml restart app

# 手动构建
docker compose -f compose.production.yml build --no-cache
```

## 📚 详细文档

查看 `PRODUCTION_GUIDE.md` 获取完整的使用指南和故障排除信息。
