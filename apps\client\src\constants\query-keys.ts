import type { Query<PERSON><PERSON> } from "@tanstack/react-query";

export const USER_KEY: QueryK<PERSON> = ["user"];
export const AUTH_PROVIDERS_KEY: QueryKey = ["auth", "providers"];

export const LANGUAGES_KEY: QueryKey = ["translation", "languages"];

export const RESUME_KEY: QueryKey = ["resume"];
export const RESUMES_KEY: QueryKey = ["resumes"];
export const RESUME_PREVIEW_KEY: QueryKey = ["resume", "preview"];
