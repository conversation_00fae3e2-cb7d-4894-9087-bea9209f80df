# Production Docker Compose Configuration for Reactive Resume
# This configuration creates a complete, self-contained environment
# that can be started and stopped as needed.

services:
  # Database (Postgres)
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - reactive-resume

  # Storage (for image uploads)
  minio:
    image: minio/minio:latest
    restart: unless-stopped
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - reactive-resume

  # Chrome Browser (for printing and previews)
  chrome:
    image: ghcr.io/browserless/chromium:latest
    restart: unless-stopped
    environment:
      HEALTH: "true"
      TOKEN: chrome_token
      PROXY_HOST: "chrome"
      PROXY_PORT: 3000
      PROXY_SSL: "false"
    networks:
      - reactive-resume

  # Main Application (Built from source)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NX_CLOUD_ACCESS_TOKEN: ""
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_healthy
      chrome:
        condition: service_started
    environment:
      # -- Environment Variables --
      PORT: 3000
      NODE_ENV: production

      # -- URLs --
      PUBLIC_URL: http://localhost:3000
      STORAGE_URL: http://localhost:9000/default

      # -- Printer (Chrome) --
      CHROME_TOKEN: chrome_token
      CHROME_URL: ws://chrome:3000

      # -- Database (Postgres) --
      DATABASE_URL: ********************************************/postgres

      # -- Auth --
      ACCESS_TOKEN_SECRET: x5y80Lfwo3h9vhvWE6jcg/PLU2KbeYNBkfHVg7RGcZBYu0IJ+OEoj14Cd/rb8eYcFE3hWIwouH0WqWcJPh3m+w==
      REFRESH_TOKEN_SECRET: HGqz/n1og/ZN6Ga8PsVmm0XGHfDkqraoW39vEmKpyXMrDqsFIAiYs4S9lhxnfIn4YELh97Xh6APQg5JsFhnbuQ==

      # -- Emails --
      MAIL_FROM: noreply@localhost
      # SMTP_URL: smtp://user:pass@smtp:587 # Optional

      # -- Storage (Minio) --
      STORAGE_ENDPOINT: minio
      STORAGE_PORT: 9000
      STORAGE_REGION: us-east-1
      STORAGE_BUCKET: default
      STORAGE_ACCESS_KEY: minioadmin
      STORAGE_SECRET_KEY: minioadmin
      STORAGE_USE_SSL: "false"
      STORAGE_SKIP_BUCKET_CHECK: "false"

      # -- Feature Flags --
      DISABLE_SIGNUPS: "false"
      DISABLE_EMAIL_AUTH: "false"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - reactive-resume

volumes:
  minio_data:
    driver: local
  postgres_data:
    driver: local

networks:
  reactive-resume:
    driver: bridge
