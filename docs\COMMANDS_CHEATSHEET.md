# Reactive Resume 命令速查表

## 🚀 启动命令

```bash
# === 完整启动流程 ===
nvm use 24.1.0                                    # 切换 Node.js 版本
docker compose -f compose.dev.yml up -d           # 启动 Docker 服务
pnpm prisma:generate                              # 生成 Prisma 客户端
pnpm prisma:migrate:dev                           # 运行数据库迁移
pnpm dev                                          # 启动开发服务器

# === 快速启动（如果环境已准备好）===
docker compose -f compose.dev.yml up -d && pnpm dev
```

## ⏹️ 停止命令

```bash
# === 停止开发服务器 ===
Ctrl + C                                          # 在运行 pnpm dev 的终端中

# === 停止 Docker 服务 ===
docker compose -f compose.dev.yml down            # 保留数据
docker compose -f compose.dev.yml down -v         # 删除数据（危险）
```

## 🔍 状态检查命令

```bash
# === 环境检查 ===
node --version                                    # 检查 Node.js 版本
pnpm --version                                    # 检查 pnpm 版本
docker --version                                  # 检查 Docker 版本

# === 服务状态 ===
docker ps                                        # 查看运行中的容器
curl http://localhost:3000/api/health            # 检查后端健康状态
curl http://localhost:5173                       # 检查前端状态

# === 端口检查 ===
netstat -ano | findstr :5173                     # 前端端口
netstat -ano | findstr :3000                     # 后端端口
netstat -ano | findstr :6173                     # Artboard 端口
netstat -ano | findstr :5432                     # 数据库端口
netstat -ano | findstr :9000                     # MinIO 端口
```

## 🛠️ 故障排除命令

```bash
# === 结束占用端口的进程 ===
taskkill /PID <进程ID> /F                         # 结束指定进程

# === 重启 Docker 容器 ===
docker restart reactive-resume-postgres-1         # 重启数据库
docker restart reactive-resume-minio-1            # 重启文件存储
docker restart reactive-resume-chrome-1           # 重启浏览器服务

# === 查看容器日志 ===
docker logs reactive-resume-postgres-1            # 数据库日志
docker logs reactive-resume-minio-1               # MinIO 日志
docker logs reactive-resume-chrome-1              # Chrome 日志

# === 重新安装依赖 ===
npm install -g pnpm                               # 重新安装 pnpm
pnpm install                                      # 重新安装项目依赖
```

## 🗄️ 数据库命令

```bash
# === Prisma 相关 ===
pnpm prisma:generate                              # 生成客户端
pnpm prisma:migrate:dev                           # 开发环境迁移
pnpm prisma studio                                # 打开数据库管理界面
pnpm exec prisma migrate reset                    # 重置数据库（危险）

# === 数据库连接测试 ===
pnpm exec prisma db pull                          # 从数据库拉取 schema
pnpm exec prisma validate                         # 验证 schema
```

## 🔄 重启命令组合

```bash
# === 快速重启 ===
# 1. 停止
Ctrl + C
docker compose -f compose.dev.yml down

# 2. 启动
docker compose -f compose.dev.yml up -d
pnpm dev

# === 完整重启 ===
# 1. 完全停止
docker compose -f compose.dev.yml down

# 2. 环境检查
nvm use 24.1.0
npm install -g pnpm

# 3. 重新启动
docker compose -f compose.dev.yml up -d
pnpm prisma:generate
pnpm dev

# === 一键重启脚本 ===
docker compose -f compose.dev.yml down && nvm use 24.1.0 && docker compose -f compose.dev.yml up -d && pnpm dev
```

## 📊 监控命令

```bash
# === 实时监控 ===
docker stats                                      # Docker 容器资源使用
docker ps -a                                      # 所有容器状态

# === 系统资源 ===
tasklist | findstr node                           # Node.js 进程
tasklist | findstr docker                         # Docker 进程
```

## 🧹 清理命令

```bash
# === Docker 清理 ===
docker system prune                               # 清理未使用的 Docker 资源
docker volume prune                               # 清理未使用的数据卷
docker image prune                                # 清理未使用的镜像

# === Node.js 清理 ===
pnpm store prune                                  # 清理 pnpm 缓存
npm cache clean --force                           # 清理 npm 缓存
```

## 🎯 常用访问地址

```bash
# === 主要服务 ===
http://localhost:5173                             # 前端应用
http://localhost:3000/api/health                  # 后端健康检查
http://localhost:6173/artboard/builder            # Artboard 编辑器

# === 管理界面 ===
http://localhost:9001                             # MinIO 控制台 (admin/admin)
http://localhost:5555                             # Prisma Studio (如果启动)
```

## ⚡ 一键命令

```bash
# === 环境准备 ===
nvm use 24.1.0 && npm install -g pnpm

# === 启动项目 ===
docker compose -f compose.dev.yml up -d && pnpm prisma:generate && pnpm dev

# === 停止项目 ===
# Ctrl+C 然后运行:
docker compose -f compose.dev.yml down

# === 状态检查 ===
echo "Node: $(node --version)" && echo "Docker containers:" && docker ps && echo "Ports:" && netstat -ano | findstr ":5173\|:3000\|:6173"

# === 健康检查 ===
curl -s http://localhost:3000/api/health && echo " - Backend OK" || echo " - Backend Error"
```

## 📝 注意事项

- ✅ **按顺序执行**: 先启动 Docker 服务，再启动开发服务器
- ⚠️ **等待时间**: Docker 服务启动需要 10-30 秒
- 🔄 **重启解决**: 大部分问题可通过重启解决
- 💾 **数据安全**: 避免使用 `down -v` 除非确定要删除数据
- 🚫 **避免冲突**: 确保端口未被其他程序占用

---

**提示**: 将常用命令保存为批处理文件或 PowerShell 脚本可以提高效率！
