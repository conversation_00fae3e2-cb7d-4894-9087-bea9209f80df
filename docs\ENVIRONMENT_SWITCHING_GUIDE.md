# Reactive Resume 环境切换指南

## 📋 概述

本文档提供开发环境与生产环境之间切换的详细步骤。两个环境使用相同的Docker基础服务（PostgreSQL、MinIO、Chrome），但应用运行方式不同：

- **开发环境**: 使用 `pnpm dev` 运行源码，支持热重载
- **生产环境**: 使用Docker容器运行编译后的应用

## 🏗️ 当前环境状态

### Docker 资源概览
```
镜像 (5个):
├── reactive-resume-app:latest (1.73GB) - 生产环境应用镜像
├── ghcr.io/browserless/chromium:latest (5.24GB) - Chrome浏览器服务
├── node:lts-alpine (226MB) - Node.js基础镜像
├── postgres:16-alpine (394MB) - PostgreSQL数据库
└── minio/minio:latest (240MB) - 文件存储服务

容器 (4个):
├── reactive-resume-app-1 - 生产环境应用容器
├── reactive-resume-chrome-1 - Chrome浏览器容器
├── reactive-resume-postgres-1 - PostgreSQL数据库容器
└── reactive-resume-minio-1 - MinIO存储容器

数据卷 (2个):
├── reactive-resume_postgres_data (数据库数据)
└── reactive-resume_minio_data (文件存储数据)
```

## 🔄 环境切换步骤

### 从生产环境切换到开发环境

#### 1. 停止生产环境容器
```bash
# 停止生产环境所有容器（保留数据）
docker compose -f compose.production.yml stop

# 启动开发环境容器（带端口映射）
docker compose -f compose.dev.yml up -d
```

#### 2. 确保Node.js环境正确
```bash
# 切换到开发版本
nvm use 24.1.0

# 确认版本
node --version  # 应显示 v24.1.0
```

#### 3. 启动开发服务器
```bash
# 生成Prisma客户端（如果需要）
pnpm prisma:generate

# 运行数据库迁移（如果有新的迁移）
pnpm prisma:migrate:dev

# 启动开发服务器
pnpm dev
```

#### 4. 验证开发环境
```bash
# 检查服务状态
curl http://localhost:3000/api/health
curl http://localhost:5173  # 前端开发服务器
curl http://localhost:6173/artboard/  # 画板开发服务器
```

### 从开发环境切换到生产环境

#### 1. 停止开发服务器
```bash
# 在运行 pnpm dev 的终端中按 Ctrl+C
```

#### 2. 启动生产环境应用容器
```bash
# 启动应用容器（数据库和存储服务应该已经在运行）
docker start reactive-resume-app-1

# 或者使用 compose 启动整个生产环境
docker compose -f compose.production.yml up -d
```

#### 3. 验证生产环境
```bash
# 检查容器状态
docker ps

# 检查应用健康状态
curl http://localhost:3000/api/health

# 访问生产应用
curl http://localhost:3000
```

## 🚀 快速切换命令

### 开发环境 → 生产环境
```bash
# 一键切换到生产环境（先停止可能运行的应用容器）
docker stop reactive-resume-app-1 2>nul || echo "容器未运行"
docker compose -f compose.production.yml up -d
```

### 生产环境 → 开发环境  
```bash
# 一键切换到开发环境
docker stop reactive-resume-app-1
nvm use 24.1.0 && pnpm dev
```

## 📊 端口使用说明

### 开发环境端口
- `3000` - 后端API服务器
- `5173` - 前端开发服务器 (Vite)
- `6173` - 画板开发服务器 (Vite)
- `5432` - PostgreSQL数据库
- `9000-9001` - MinIO存储服务

### 生产环境端口
- `3000` - 完整应用 (前端+后端)
- `5432` - PostgreSQL数据库
- `9000-9001` - MinIO存储服务

## 🔧 数据持久化

### 数据保留策略
- **数据库数据**: 存储在 `reactive-resume_postgres_data` volume中
- **文件存储数据**: 存储在 `reactive-resume_minio_data` volume中
- **用户数据**: 包括简历、上传的图片等，在环境切换时完全保留

### 数据清理（谨慎操作）
```bash
# 仅在需要完全重置时使用
docker compose -f compose.production.yml down -v  # 会删除所有数据！
```

## ⚠️ 注意事项

### 环境切换最佳实践
1. **避免同时运行**: 不要同时运行开发和生产环境，会导致端口冲突
2. **数据一致性**: 两个环境共享相同的数据库，确保数据库迁移兼容性
3. **依赖同步**: 确保开发环境的依赖与生产环境构建时的依赖一致
4. **容器配置差异**: 开发环境容器有端口映射，生产环境容器在内部网络运行

### 常见问题解决
```bash
# 端口被占用
netstat -ano | findstr :3000  # 查找占用进程
taskkill /PID <进程ID> /F      # 终止进程

# 容器启动失败
docker logs reactive-resume-app-1  # 查看日志
docker restart reactive-resume-app-1  # 重启容器

# 数据库连接问题
docker restart reactive-resume-postgres-1  # 重启数据库
```

## 🎯 推荐工作流程

### 日常开发
1. 使用开发环境进行代码开发和调试
2. 定期切换到生产环境测试完整功能
3. 重要功能完成后，重新构建生产镜像

### 生产部署前测试
1. 构建最新的生产镜像
2. 在生产环境中完整测试所有功能
3. 确认数据迁移和API兼容性

---

**创建日期**: 2025年1月15日
**适用版本**: Reactive Resume v4.4.6
**Docker版本**: 支持Docker Compose v2
