# Reactive Resume 快速使用指南

## 🚀 项目启动

### 1. 环境准备
```bash
# 切换到正确的 Node.js 版本
nvm use 24.1.0

# 如果没有安装该版本
nvm install 24.1.0
nvm use 24.1.0

# 确保 pnpm 可用
npm install -g pnpm
```

### 2. 启动服务
```bash
# 启动 Docker 服务（数据库、存储等）
docker compose -f compose.dev.yml up -d

# 生成 Prisma 客户端
pnpm prisma:generate

# 运行数据库迁移
pnpm prisma:migrate:dev

# 启动开发服务器
pnpm dev
```

### 3. 验证启动
启动成功后，您应该看到：
```
✓ Server is up and running on port 3000
✓ Local: http://localhost:5173/
✓ Local: http://localhost:6173/artboard/
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **主应用** | http://localhost:5173 | 用户界面，注册登录，管理简历 |
| **API 健康检查** | http://localhost:3000/api/health | 后端服务状态 |
| **Artboard** | http://localhost:6173/artboard/builder | 简历编辑器 |
| **MinIO 控制台** | http://localhost:9001 | 文件存储管理 (admin/admin) |

## ⏹️ 项目停止

### 停止开发服务器
在运行 `pnpm dev` 的终端中按 `Ctrl + C`

### 停止 Docker 服务
```bash
docker compose -f compose.dev.yml down
```

### 完全清理（会删除数据）
```bash
docker compose -f compose.dev.yml down -v
```

## 🔄 重启项目

### 快速重启
```bash
# 1. 停止服务
Ctrl + C  # 停止开发服务器
docker compose -f compose.dev.yml down

# 2. 重新启动
docker compose -f compose.dev.yml up -d
pnpm dev
```

### 完整重启（解决大部分问题）
```bash
# 1. 完全停止
docker compose -f compose.dev.yml down

# 2. 确保环境
nvm use 24.1.0
npm install -g pnpm

# 3. 重新启动
docker compose -f compose.dev.yml up -d
pnpm prisma:generate
pnpm dev
```

## 🔍 状态检查

### 检查 Docker 容器
```bash
docker ps
```
应该看到：
- reactive-resume-postgres-1
- reactive-resume-minio-1  
- reactive-resume-chrome-1

### 检查端口占用
```bash
# Windows
netstat -ano | findstr :5173
netstat -ano | findstr :3000
netstat -ano | findstr :6173

# 如果端口被占用，结束进程
taskkill /PID <进程ID> /F
```

### 检查服务健康
```bash
# 检查后端 API
curl http://localhost:3000/api/health

# 检查前端
curl http://localhost:5173
```

## 🛠️ 常见问题解决

### 1. 端口被占用
```bash
# 查找占用进程
netstat -ano | findstr :5173

# 结束进程
taskkill /PID <进程ID> /F

# 或者重启计算机
```

### 2. Node.js 版本问题
```bash
# 检查当前版本
node --version

# 切换到正确版本
nvm use 24.1.0

# 重新安装 pnpm
npm install -g pnpm
```

### 3. 数据库连接失败
```bash
# 重启数据库容器
docker restart reactive-resume-postgres-1

# 检查容器日志
docker logs reactive-resume-postgres-1

# 重新运行迁移
pnpm prisma:migrate:dev
```

### 4. 文件上传失败
```bash
# 重启 MinIO 容器
docker restart reactive-resume-minio-1

# 访问 MinIO 控制台检查
# http://localhost:9001 (用户名/密码: admin/admin)
```

### 5. PDF 生成失败
```bash
# 重启 Chrome 容器
docker restart reactive-resume-chrome-1

# 检查容器状态
docker ps | grep chrome
```

## 📝 开发命令

### 数据库相关
```bash
# 生成 Prisma 客户端
pnpm prisma:generate

# 开发环境迁移
pnpm prisma:migrate:dev

# 重置数据库（谨慎使用）
pnpm exec prisma migrate reset

# 查看数据库
pnpm prisma studio
```

### 构建和测试
```bash
# 构建项目
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 格式化代码
pnpm format:fix
```

## ⚠️ 重要注意事项

### 数据安全
- ✅ **正常停止**: `Ctrl+C` + `docker compose down` 会保留数据
- ❌ **危险操作**: `docker compose down -v` 会删除所有数据
- 💾 **备份建议**: 定期备份重要的简历数据

### 性能优化
- 🔋 **资源占用**: 项目会占用较多 CPU 和内存
- 🚫 **不用时停止**: 不使用时建议停止服务释放资源
- 💻 **推荐配置**: 8GB+ 内存，4核+ CPU

### 网络访问
- 🏠 **本地访问**: 默认只能从本机访问
- 🌐 **外部访问**: 如需外部访问需要修改配置
- 🔒 **安全提醒**: 开发模式不适合生产环境

### 环境要求
- ✅ **Node.js**: v24.1.0 (使用 nvm 管理)
- ✅ **Docker**: 确保 Docker Desktop 正在运行
- ✅ **pnpm**: 全局安装的包管理器

## 🎯 使用流程

### 首次使用
1. **启动项目**: 按照启动步骤操作
2. **访问应用**: http://localhost:5173
3. **注册账户**: 创建新用户账户
4. **创建简历**: 开始制作您的简历

### 日常使用
1. **启动**: `docker compose -f compose.dev.yml up -d` + `pnpm dev`
2. **使用**: 访问 http://localhost:5173
3. **停止**: `Ctrl+C` + `docker compose -f compose.dev.yml down`

### 故障排除
1. **检查状态**: 运行状态检查命令
2. **查看日志**: 检查终端输出和容器日志
3. **重启服务**: 按照重启步骤操作
4. **完全重置**: 必要时进行完整重启

## 📞 获取帮助

如果遇到问题：
1. 📋 **检查本指南**: 查看常见问题解决方案
2. 🔍 **查看日志**: 检查终端和容器日志输出
3. 🔄 **尝试重启**: 大部分问题可通过重启解决
4. 💻 **检查环境**: 确认 Node.js、Docker 等环境正确

---

**最后更新**: 2025年6月8日  
**适用版本**: Reactive Resume v4.4.6  
**推荐环境**: Windows 11 + Node.js v24.1.0 + Docker Desktop
