# Prisma 命令详解

## 📚 概述

本文档详细解释 Reactive Resume 项目中使用的 Prisma 命令，帮助开发者理解数据库操作的原理和使用场景。

## 🎯 核心命令

### 1. `pnpm prisma:generate` - 生成Prisma客户端

#### 🔍 **作用**
根据 `tools/prisma/schema.prisma` 文件生成TypeScript类型安全的数据库客户端代码。

#### 📁 **生成内容**
- **位置**: `node_modules/.prisma/client/`
- **类型定义**: 为每个数据模型生成TypeScript类型
- **查询方法**: 为每个模型生成CRUD操作方法
- **关系映射**: 处理模型间的关联关系

#### 💻 **使用示例**
```typescript
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 类型安全的查询
const user = await prisma.user.findUnique({
  where: { email: '<EMAIL>' }
})

const resumes = await prisma.resume.findMany({
  where: { userId: user.id },
  include: { statistics: true }
})
```

#### 🕐 **何时运行**
- ✅ 首次设置项目时
- ✅ 修改了 `schema.prisma` 文件后
- ✅ 环境切换时（确保客户端代码最新）
- ✅ `node_modules` 被删除重装后
- ✅ 拉取了包含schema变更的代码后

---

### 2. `pnpm prisma:migrate:dev` - 开发环境数据库迁移

#### 🔍 **作用**
将数据库结构同步到最新状态，确保数据库表结构与 `schema.prisma` 文件一致。

#### 📁 **迁移文件**
项目中的迁移文件位于 `tools/prisma/migrations/`：
```
migrations/
├── 20231121234455_initialize_tables/
├── 20240505101746_add_statistics_table/
├── 20240507090221_make_last_signed_in_non_null/
├── 20250112140257_normalize_user_email_fields/
└── 20250113145008_add_openid_provider_to_enums/
```

#### 🔄 **执行流程**
1. **检查迁移状态**: 查看哪些迁移还没有应用
2. **应用未执行的迁移**: 按时间顺序执行SQL文件
3. **更新迁移记录**: 在 `_prisma_migrations` 表中记录
4. **自动生成客户端**: 迁移完成后自动运行 `prisma generate`

#### 🕐 **何时运行**
- ✅ 首次设置项目时（创建所有表）
- ✅ 拉取了包含新迁移的代码后
- ✅ 数据库被重置或清空后
- ✅ 切换到开发环境时（确保数据库结构最新）

---

## 🤔 **为什么需要这两个命令？**

### **数据库与代码同步**

#### **Schema文件** (`tools/prisma/schema.prisma`)
```prisma
model User {
  id               String   @id @default(cuid())
  name             String
  username         String   @unique
  email            String   @unique
  resumes          Resume[]
}

model Resume {
  id         String      @id @default(cuid())
  title      String
  slug       String
  data       Json        @default("{}")
  userId     String
  user       User        @relation(fields: [userId], references: [id])
}
```

#### **迁移文件** (`migrations/xxx/migration.sql`)
```sql
-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Resume" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "data" JSONB NOT NULL DEFAULT '{}',
    "userId" TEXT NOT NULL,
    CONSTRAINT "Resume_pkey" PRIMARY KEY ("id")
);
```

#### **生成的客户端代码**
```typescript
export type User = {
  id: string
  name: string
  username: string
  email: string
}

export type Resume = {
  id: string
  title: string
  slug: string
  data: JsonValue
  userId: string
}
```

## 🔄 **环境切换场景**

### **从生产环境切换到开发环境时**

1. **数据库可能不同步**:
   - 生产环境可能有新的迁移已应用
   - 开发环境的数据库结构可能落后

2. **客户端代码可能过期**:
   - `node_modules` 中的Prisma客户端可能是旧版本
   - Schema定义可能已更新

### **执行顺序**
```bash
# 1. 确保数据库结构最新
pnpm prisma:migrate:dev
# → 检查并应用所有未执行的迁移
# → 确保数据库表结构与schema.prisma一致

# 2. 生成最新的客户端代码  
pnpm prisma:generate
# → 根据最新的schema生成TypeScript客户端
# → 确保代码中的类型定义正确
```

## 🎯 **简化理解**

### **类比说明**
想象数据库是一个图书馆：

- **Schema文件** = 图书馆的设计图纸
- **迁移文件** = 装修施工指令
- **`migrate:dev`** = 按照指令装修图书馆
- **`generate`** = 制作图书馆的使用手册

### **实际作用**
- **`prisma:migrate:dev`** = "更新数据库的表结构"
- **`prisma:generate`** = "更新代码中的数据库操作工具"

## ⚠️ **使用建议**

### **何时可以跳过**
如果你确定：
- ✅ 数据库结构没有变化
- ✅ Schema文件没有修改  
- ✅ 最近没有拉取新代码
- ✅ `node_modules` 没有重装

那么可以直接运行 `pnpm dev`。

### **安全做法**
为了保险起见，环境切换时运行这两个命令是最佳实践：
- 执行很快（通常几秒钟）
- 能避免很多潜在问题
- 确保开发环境状态正确

## 🔧 **其他相关命令**

### **生产环境迁移**
```bash
pnpm prisma:migrate  # 等同于 prisma migrate deploy
```

### **数据库重置**
```bash
pnpm exec prisma migrate reset  # 谨慎使用，会删除所有数据
```

### **数据库管理**
```bash
pnpm prisma studio  # 打开数据库可视化管理界面
```

---

**创建日期**: 2025年1月15日  
**适用版本**: Reactive Resume v4.4.6  
**Prisma版本**: v5.22.0
