# Reactive Resume 文档与工具

本目录包含 Reactive Resume 项目的使用文档和便捷工具脚本。

## 📁 目录结构

```
docs/dev/
├── README.md                    # 本说明文件
├── QUICK_START_GUIDE.md         # 快速启动指南
├── USAGE_GUIDE.md               # 详细使用指南
├── PROJECT_SETUP.md             # 项目设置说明
├── COMMANDS_CHEATSHEET.md       # 命令速查表
├── PRISMA_COMMANDS_EXPLAINED.md # Prisma命令详解
├── SCRIPT_STATUS.md             # 脚本状态说明
├── start.bat / start-en.bat     # 一键启动脚本
├── stop.bat / stop-en.bat       # 一键停止脚本
├── status.bat / status-en.bat   # 状态检查脚本
├── daily-start.bat              # 日常启动脚本
├── daily-stop.bat               # 日常停止脚本
├── test-scripts.bat             # 脚本测试工具
└── test-artboard.html           # Artboard 测试页面
```

## 📚 文件说明

### 📖 文档文件

- **`QUICK_START_GUIDE.md`** - 快速启动指南
  - 最简化的启动流程
  - 常用命令和地址
  - 快速故障排除

- **`USAGE_GUIDE.md`** - 详细使用指南
  - 完整的环境要求和安装步骤
  - 启动、使用、停止流程
  - 常见问题解决方案
  - 开发命令参考

- **`PRISMA_COMMANDS_EXPLAINED.md`** - Prisma命令详解
  - `prisma:generate` 和 `prisma:migrate:dev` 详细说明
  - 数据库迁移原理
  - 环境切换时的数据库操作

- **`PROJECT_SETUP.md`** - 项目设置说明
- **`COMMANDS_CHEATSHEET.md`** - 命令速查表

### 🚀 脚本工具

- **`start-en.bat`** - 一键启动脚本 (推荐使用)
  - 自动检查环境
  - 启动 Docker 服务
  - 启动开发服务器
  - 显示访问地址

- **`stop-en.bat`** - 一键停止脚本 (推荐使用)
  - 停止 Docker 服务
  - 检查端口状态
  - 清理资源

- **`status-en.bat`** - 状态检查脚本 (推荐使用)
  - 检查环境配置
  - 显示容器状态
  - 检查端口占用
  - 测试服务健康状态

- **`start.bat`**, **`stop.bat`**, **`status.bat`** - 中文版脚本 (可能有编码问题)

### 🧪 测试工具

- **`test-artboard.html`** - Artboard 测试页面
  - 设置测试简历数据
  - 验证 Artboard 功能
  - 调试工具

## 🎯 快速开始

### 启动项目
```bash
# 双击运行启动脚本 (推荐)
docs\start-en.bat

# 或在命令行中运行
cd docs
start-en.bat
```

### 检查状态
```bash
# 双击运行状态检查脚本
docs\status-en.bat
```

### 停止项目
```bash
# 双击运行停止脚本
docs\stop-en.bat
```

## 📋 使用流程

1. **首次使用**:
   - 阅读 `USAGE_GUIDE.md` 了解详细说明
   - 运行 `start.bat` 启动项目
   - 访问 http://localhost:5173 开始使用

2. **日常使用**:
   - 运行 `start.bat` 启动
   - 使用完毕后运行 `stop.bat` 停止

3. **问题排查**:
   - 运行 `status.bat` 检查状态
   - 查看 `USAGE_GUIDE.md` 中的常见问题部分

## 🔗 访问地址

启动成功后可访问以下地址：

- **主应用**: http://localhost:5173
- **API 健康检查**: http://localhost:3000/api/health
- **Artboard**: http://localhost:6173/artboard/builder
- **MinIO 控制台**: http://localhost:9001 (用户名/密码: minioadmin)

## ⚠️ 注意事项

1. **权限要求**: 脚本需要管理员权限来操作 Docker 和端口
2. **环境依赖**: 确保已安装 Node.js、Docker 和 pnpm
3. **资源占用**: 项目会占用较多系统资源，不使用时建议停止
4. **数据安全**: 使用 `docker compose down -v` 会删除所有数据

## 📞 支持

如有问题，请：
1. 查看 `USAGE_GUIDE.md` 中的常见问题部分
2. 运行 `status.bat` 检查系统状态
3. 查看项目的 GitHub Issues

---

**最后更新**: 2025年1月15日
**适用版本**: Reactive Resume v4.4.6
