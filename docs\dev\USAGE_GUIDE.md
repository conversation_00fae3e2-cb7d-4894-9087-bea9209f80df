# Reactive Resume 项目使用指南

## 📋 目录
- [环境要求](#环境要求)
- [项目启动](#项目启动)
- [项目使用](#项目使用)
- [项目停止](#项目停止)
- [常见问题](#常见问题)
- [开发命令](#开发命令)

## 🔧 环境要求

### 必需软件
- **Node.js**: v22.13.1+ (推荐使用 nvm 管理版本)
- **pnpm**: 包管理器
- **Docker**: 用于运行数据库和相关服务
- **Git**: 版本控制

### 验证环境
```bash
node --version    # 应显示 v22.13.1 或更高版本
pnpm --version    # 应显示 pnpm 版本
docker --version  # 应显示 Docker 版本
```

## 🚀 项目启动

### 1. 切换到正确的 Node.js 版本
```bash
# 使用 nvm 切换到推荐版本
nvm use 22.13.1

# 如果没有安装该版本，先安装
nvm install 22.13.1
nvm use 22.13.1
```

### 2. 安装依赖
```bash
# 确保在项目根目录
cd c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume

# 安装项目依赖
pnpm install
```

### 3. 启动 Docker 服务
```bash
# 启动开发环境所需的服务（数据库、存储、浏览器）
docker compose -f compose.dev.yml up -d
```

### 4. 初始化数据库
```bash
# 生成 Prisma 客户端
pnpm prisma:generate

# 运行数据库迁移
pnpm prisma:migrate:dev
```

### 5. 启动开发服务器
```bash
# 启动所有开发服务（前端、后端、Artboard）
pnpm dev
```

### 6. 验证启动状态
启动成功后，您应该看到以下服务运行：

- **前端应用**: http://localhost:5173
- **后端 API**: http://localhost:3000/api/health
- **Artboard**: http://localhost:6173/artboard/builder
- **数据库**: localhost:5432 (PostgreSQL)
- **文件存储**: http://localhost:9000 (MinIO)
- **浏览器服务**: localhost:8081 (Chrome)

## 💻 项目使用

### 访问应用
1. **打开浏览器访问**: http://localhost:5173
2. **注册新账户**或**登录现有账户**
3. **创建新简历**或**管理现有简历**

### 主要功能
- ✅ **用户注册/登录**: 创建账户并管理个人简历
- ✅ **简历编辑**: 可视化编辑器，支持多种模板
- ✅ **实时预览**: 编辑时实时查看简历效果
- ✅ **PDF 导出**: 将简历导出为 PDF 文件
- ✅ **简历分享**: 生成公开链接分享简历
- ✅ **数据导入**: 支持从其他格式导入简历数据

### 服务说明
- **前端 (Client)**: 用户界面，处理用户交互
- **后端 (Server)**: API 服务，处理业务逻辑和数据存储
- **Artboard**: 简历渲染引擎，负责简历的可视化显示
- **PostgreSQL**: 数据库，存储用户和简历数据
- **MinIO**: 对象存储，存储图片等文件
- **Chrome**: 无头浏览器，用于 PDF 生成

## ⏹️ 项目停止

### 1. 停止开发服务器
在运行 `pnpm dev` 的终端中按 `Ctrl + C` 停止开发服务器。

### 2. 停止 Docker 服务
```bash
# 停止并移除容器
docker compose -f compose.dev.yml down

# 如果需要同时删除数据卷（会丢失数据库数据）
docker compose -f compose.dev.yml down -v
```

### 3. 验证停止状态
```bash
# 检查是否还有容器在运行
docker ps

# 检查端口是否已释放
netstat -ano | findstr :5173
netstat -ano | findstr :3000
netstat -ano | findstr :6173
```

## 🔄 快速重启

### 完整重启流程
```bash
# 1. 停止所有服务
docker compose -f compose.dev.yml down

# 2. 确保使用正确的 Node.js 版本
nvm use 24.1.0

# 3. 重新安装 pnpm（如果需要）
npm install -g pnpm

# 4. 启动 Docker 服务
docker compose -f compose.dev.yml up -d

# 5. 等待服务启动完成（约30秒）
docker ps

# 6. 启动开发服务器
pnpm dev
```

## 🛠️ 开发命令

### 数据库相关
```bash
# 生成 Prisma 客户端
pnpm prisma:generate

# 开发环境数据库迁移
pnpm prisma:migrate:dev

# 生产环境数据库迁移
pnpm prisma:migrate

# 重置数据库（谨慎使用）
pnpm exec prisma migrate reset
```

### 构建相关
```bash
# 构建所有应用
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 代码格式化
pnpm format:fix
```

### Docker 相关
```bash
# 查看容器状态
docker ps

# 查看容器日志
docker logs reactive-resume-postgres-1
docker logs reactive-resume-minio-1
docker logs reactive-resume-chrome-1

# 重启特定容器
docker restart reactive-resume-postgres-1

# 清理未使用的 Docker 资源
docker system prune
```

## ❗ 常见问题

### 1. 端口被占用
**问题**: 启动时提示端口被占用
**解决**: 
```bash
# 查找占用端口的进程
netstat -ano | findstr :5173
netstat -ano | findstr :3000

# 结束进程（替换 PID）
taskkill /PID <进程ID> /F
```

### 2. Node.js 版本警告
**问题**: 显示 "Unsupported engine" 警告
**解决**:
```bash
nvm use 24.1.0
npm install -g pnpm
```

### 3. 数据库连接失败
**问题**: 无法连接到数据库
**解决**: 
```bash
# 检查 PostgreSQL 容器状态
docker ps | grep postgres

# 重启数据库容器
docker restart reactive-resume-postgres-1

# 检查数据库健康状态
curl http://localhost:3000/api/health
```

### 4. 文件上传失败
**问题**: 图片上传失败
**解决**: 
```bash
# 检查 MinIO 容器状态
docker ps | grep minio

# 重启 MinIO 容器
docker restart reactive-resume-minio-1

# 访问 MinIO 控制台
# http://localhost:9001 (用户名: minioadmin, 密码: minioadmin)
```

### 5. PDF 生成失败
**问题**: 无法生成 PDF
**解决**: 
```bash
# 检查 Chrome 容器状态
docker ps | grep chrome

# 重启 Chrome 容器
docker restart reactive-resume-chrome-1
```

## 📝 注意事项

1. **数据持久化**: Docker 容器重启后数据会保留，但使用 `docker compose down -v` 会删除所有数据
2. **开发模式**: 当前配置为开发模式，不适合生产环境使用
3. **网络访问**: 默认只能从本机访问，如需外部访问需要修改配置
4. **资源占用**: 项目会占用较多系统资源，建议在性能较好的机器上运行

## 🔗 相关链接

- **项目主页**: https://github.com/AmruthPillai/Reactive-Resume
- **文档**: 项目 README.md
- **问题反馈**: GitHub Issues

---

**最后更新**: 2025年1月15日
**适用版本**: Reactive Resume v4.4.6
