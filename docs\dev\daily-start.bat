@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 日常启动
echo ========================================
echo.

:: 切换到项目目录
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: 检查 Docker 容器状态
echo [1/3] 检查 Docker 容器状态...
docker ps | findstr reactive-resume >nul
if errorlevel 1 (
    echo 容器未运行，正在启动...
    docker compose -f compose.dev.yml up -d
    echo 等待容器启动完成...
    timeout /t 10 /nobreak >nul
) else (
    echo 容器已在运行，跳过启动步骤
)

:: 检查 Node.js 版本
echo.
echo [2/3] 检查 Node.js 版本...
node --version | findstr "v24" >nul
if errorlevel 1 (
    echo 切换到 Node.js v24.1.0...
    nvm use 24.1.0
) else (
    echo Node.js 版本正确
)

:: 启动开发服务器
echo.
echo [3/3] 启动开发服务器...
echo.
echo ========================================
echo    准备完成！启动开发服务器...
echo ========================================
echo.
echo 访问地址:
echo   前端应用: http://localhost:5173
echo   后端 API: http://localhost:3000/api/health
echo   Artboard: http://localhost:6173/artboard/builder
echo.
echo 按 Ctrl+C 停止开发服务器
echo ========================================
echo.

pnpm dev
