@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 日常停止
echo ========================================
echo.

:: 切换到项目目录
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

echo 选择停止方式:
echo   1. 仅停止开发服务器（推荐，保持容器运行）
echo   2. 停止容器但保留数据（节省资源）
echo   3. 完全停止并清理数据（危险，会删除所有数据）
echo   0. 取消
echo.
set /p choice=请选择 (0-3): 

if "%choice%"=="1" (
    echo.
    echo 开发服务器已通过 Ctrl+C 停止
    echo Docker 容器继续运行，下次启动会更快
    echo 数据已保留
) else if "%choice%"=="2" (
    echo.
    echo 停止 Docker 容器但保留数据...
    docker compose -f compose.dev.yml stop
    echo 容器已停止，数据已保留
    echo 下次使用 daily-start.bat 会自动重启容器
) else if "%choice%"=="3" (
    echo.
    echo ⚠️  警告：这将删除所有数据！
    set /p confirm=确认删除所有数据？(y/N): 
    if /i "%confirm%"=="y" (
        docker compose -f compose.dev.yml down -v
        echo 所有容器和数据已删除
    ) else (
        echo 操作已取消
    )
) else if "%choice%"=="0" (
    echo 操作已取消
) else (
    echo 无效选择
)

echo.
pause
