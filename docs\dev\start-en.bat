@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume Start Script
echo ========================================
echo.

:: Switch to project directory
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: Check Node.js version
echo [1/6] Checking Node.js version...
node --version | findstr "v2[2-9]" >nul
if errorlevel 1 (
    echo Warning: Recommend Node.js v22.13.1+
    echo Trying to switch to recommended version...
    call nvm use 22.13.1
    if errorlevel 1 (
        echo Error: Cannot switch to Node.js v22.13.1
        echo Please run manually: nvm install 22.13.1 && nvm use 22.13.1
        pause
        exit /b 1
    )
) else (
    echo OK Node.js version check passed
)

:: Check pnpm
echo.
echo [2/6] Checking pnpm...
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo Installing pnpm...
    npm install -g pnpm
    if errorlevel 1 (
        echo Error: Cannot install pnpm
        pause
        exit /b 1
    )
) else (
    echo OK pnpm is installed
)

:: Start Docker services
echo.
echo [3/6] Starting Docker services...
docker compose -f compose.dev.yml up -d
if errorlevel 1 (
    echo Error: Docker services failed to start
    echo Please ensure Docker Desktop is running
    pause
    exit /b 1
)
echo OK Docker services started

:: Wait for services to start
echo.
echo [4/6] Waiting for services to start...
timeout /t 10 /nobreak >nul
echo OK Service startup wait completed

:: Generate Prisma client
echo.
echo [5/6] Generating Prisma client...
pnpm prisma:generate >nul 2>&1
if errorlevel 1 (
    echo Warning: Prisma client generation may have issues, but continuing...
) else (
    echo OK Prisma client generated
)

:: Start development server
echo.
echo [6/6] Starting development server...
echo.
echo ========================================
echo    Services Started Successfully!
echo ========================================
echo.
echo Access URLs:
echo   Frontend: http://localhost:5173
echo   Backend API: http://localhost:3000/api/health
echo   Artboard: http://localhost:6173/artboard/builder
echo.
echo Press Ctrl+C to stop development server
echo ========================================
echo.

:: Start development server
pnpm dev
