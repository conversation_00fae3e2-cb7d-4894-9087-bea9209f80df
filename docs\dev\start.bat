@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume Start Script
echo ========================================
echo.

:: 切换到项目目录
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: 检查 Node.js 版本
echo [1/6] 检查 Node.js 版本...
node --version | findstr "v2[2-9]" >nul
if errorlevel 1 (
    echo 警告: 建议使用 Node.js v22.13.1+
    echo 正在尝试切换到推荐版本...
    call nvm use 22.13.1
    if errorlevel 1 (
        echo 错误: 无法切换到 Node.js v22.13.1
        echo 请手动运行: nvm install 22.13.1 && nvm use 22.13.1
        pause
        exit /b 1
    )
) else (
    echo ✓ Node.js 版本检查通过
)

:: 检查 pnpm
echo.
echo [2/6] 检查 pnpm...
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo 正在安装 pnpm...
    npm install -g pnpm
    if errorlevel 1 (
        echo 错误: 无法安装 pnpm
        pause
        exit /b 1
    )
) else (
    echo ✓ pnpm 已安装
)

:: 启动 Docker 服务
echo.
echo [3/6] 启动 Docker 服务...
docker compose -f compose.dev.yml up -d
if errorlevel 1 (
    echo 错误: Docker 服务启动失败
    echo 请确保 Docker Desktop 正在运行
    pause
    exit /b 1
)
echo ✓ Docker 服务已启动

:: 等待服务启动
echo.
echo [4/6] 等待服务启动完成...
timeout /t 10 /nobreak >nul
echo ✓ 服务启动等待完成

:: 生成 Prisma 客户端
echo.
echo [5/6] 生成 Prisma 客户端...
pnpm prisma:generate >nul 2>&1
if errorlevel 1 (
    echo 警告: Prisma 客户端生成可能有问题，但继续启动...
) else (
    echo ✓ Prisma 客户端已生成
)

:: 启动开发服务器
echo.
echo [6/6] 启动开发服务器...
echo.
echo ========================================
echo    服务启动完成！
echo ========================================
echo.
echo 访问地址:
echo   前端应用: http://localhost:5173
echo   后端 API: http://localhost:3000/api/health
echo   Artboard: http://localhost:6173/artboard/builder
echo.
echo 按 Ctrl+C 停止开发服务器
echo ========================================
echo.

:: 启动开发服务器
pnpm dev
