@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume Status Check
echo ========================================
echo.

:: Switch to project directory
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: Check Node.js version
echo [Environment Check]
echo Node.js version:
node --version
echo.

echo pnpm version:
pnpm --version 2>nul
if errorlevel 1 echo pnpm not installed
echo.

echo Docker version:
docker --version 2>nul
if errorlevel 1 echo Docker not installed
echo.

:: Check Docker container status
echo ========================================
echo [Docker Container Status]
echo ========================================
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | findstr reactive-resume
if errorlevel 1 (
    echo No running Reactive Resume containers
)
echo.

:: Check port usage
echo ========================================
echo [Port Usage Status]
echo ========================================
echo Port 5173 (Frontend):
netstat -ano | findstr :5173
if errorlevel 1 echo   Not occupied
echo.
echo Port 3000 (Backend):
netstat -ano | findstr :3000
if errorlevel 1 echo   Not occupied
echo.
echo Port 6173 (Artboard):
netstat -ano | findstr :6173
if errorlevel 1 echo   Not occupied
echo.
echo Port 5432 (Database):
netstat -ano | findstr :5432
if errorlevel 1 echo   Not occupied
echo.
echo Port 9000 (MinIO):
netstat -ano | findstr :9000
if errorlevel 1 echo   Not occupied
echo.

:: Check service health
echo ========================================
echo [Service Health Check]
echo ========================================
echo Checking backend API health:
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3000/api/health 2>nul
if errorlevel 1 echo API not accessible
echo.
echo.
echo Checking frontend app:
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:5173 2>nul
if errorlevel 1 echo Frontend not accessible
echo.
echo.

echo ========================================
echo Status check completed
echo ========================================
pause
