@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 状态检查
echo ========================================
echo.

:: 切换到项目目录
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: 检查 Node.js 版本
echo [环境检查]
echo Node.js 版本:
node --version
echo.

echo pnpm 版本:
pnpm --version 2>nul
if errorlevel 1 echo pnpm 未安装
echo.

echo Docker 版本:
docker --version 2>nul
if errorlevel 1 echo Docker 未安装
echo.

:: 检查 Docker 容器状态
echo ========================================
echo [Docker 容器状态]
echo ========================================
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | findstr reactive-resume
if errorlevel 1 (
    echo 没有运行中的 Reactive Resume 容器
)
echo.

:: 检查端口占用
echo ========================================
echo [端口占用状态]
echo ========================================
echo 端口 5173 (前端):
netstat -ano | findstr :5173
if errorlevel 1 echo   未占用
echo.
echo 端口 3000 (后端):
netstat -ano | findstr :3000
if errorlevel 1 echo   未占用
echo.
echo 端口 6173 (Artboard):
netstat -ano | findstr :6173
if errorlevel 1 echo   未占用
echo.
echo 端口 5432 (数据库):
netstat -ano | findstr :5432
if errorlevel 1 echo   未占用
echo.
echo 端口 9000 (MinIO):
netstat -ano | findstr :9000
if errorlevel 1 echo   未占用
echo.

:: 检查服务健康状态
echo ========================================
echo [服务健康检查]
echo ========================================
echo 检查后端 API 健康状态:
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:3000/api/health 2>nul
if errorlevel 1 echo API 不可访问
echo.
echo.
echo 检查前端应用:
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:5173 2>nul
if errorlevel 1 echo 前端不可访问
echo.
echo.

echo ========================================
echo 状态检查完成
echo ========================================
pause
