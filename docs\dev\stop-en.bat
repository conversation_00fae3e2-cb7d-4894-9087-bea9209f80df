@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume Stop Script
echo ========================================
echo.

:: Switch to project directory
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: Stop Docker services
echo [1/2] Stopping Docker services...
docker compose -f compose.dev.yml down
if errorlevel 1 (
    echo Warning: Docker services stop may have issues
) else (
    echo OK Docker services stopped
)

:: Check port status
echo.
echo [2/2] Checking port status...
echo Checking port 5173 (Frontend):
netstat -ano | findstr :5173
echo.
echo Checking port 3000 (Backend):
netstat -ano | findstr :3000
echo.
echo Checking port 6173 (Artboard):
netstat -ano | findstr :6173
echo.

echo ========================================
echo    Project Stopped
echo ========================================
echo.
echo Note: If ports are still occupied, please manually end related processes
echo or restart computer to completely release ports
echo.
pause
