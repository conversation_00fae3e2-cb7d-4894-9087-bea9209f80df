<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Artboard</title>
</head>
<body>
    <h1>Artboard Test</h1>
    <button onclick="setTestData()">Set Test Resume Data</button>
    <button onclick="clearData()">Clear Data</button>
    <button onclick="openArtboard()">Open Artboard</button>
    
    <script>
        function setTestData() {
            const sampleResume = {
                "basics": {
                    "name": "<PERSON>",
                    "headline": "Creative and Innovative Web Developer",
                    "email": "<EMAIL>",
                    "phone": "(*************",
                    "location": "Pleasantville, CA 94588",
                    "url": {
                        "label": "",
                        "href": "https://johndoe.me/"
                    },
                    "customFields": [],
                    "picture": {
                        "url": "https://i.imgur.com/HgwyOuJ.jpg",
                        "size": 120,
                        "aspectRatio": 1,
                        "borderRadius": 0,
                        "effects": {
                            "hidden": false,
                            "border": false,
                            "grayscale": false
                        }
                    }
                },
                "sections": {
                    "summary": {
                        "name": "Summary",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "summary",
                        "content": "Passionate web developer with 5+ years of experience creating dynamic and responsive web applications."
                    },
                    "awards": {
                        "name": "Awards",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "awards",
                        "items": []
                    },
                    "certifications": {
                        "name": "Certifications",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "certifications",
                        "items": []
                    },
                    "education": {
                        "name": "Education",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "education",
                        "items": [
                            {
                                "id": "education-1",
                                "visible": true,
                                "institution": "University of California",
                                "studyType": "Bachelor's Degree",
                                "area": "Computer Science",
                                "score": "3.8 GPA",
                                "date": "2015 - 2019",
                                "summary": "Focused on web development and software engineering.",
                                "url": {
                                    "label": "",
                                    "href": ""
                                }
                            }
                        ]
                    },
                    "experience": {
                        "name": "Experience",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "experience",
                        "items": [
                            {
                                "id": "experience-1",
                                "visible": true,
                                "company": "Tech Solutions Inc.",
                                "position": "Senior Web Developer",
                                "location": "San Francisco, CA",
                                "date": "2020 - Present",
                                "summary": "Lead development of responsive web applications using React and Node.js.",
                                "url": {
                                    "label": "",
                                    "href": ""
                                }
                            }
                        ]
                    },
                    "volunteer": {
                        "name": "Volunteering",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "volunteer",
                        "items": []
                    },
                    "interests": {
                        "name": "Interests",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "interests",
                        "items": []
                    },
                    "languages": {
                        "name": "Languages",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "languages",
                        "items": []
                    },
                    "profiles": {
                        "name": "Profiles",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "profiles",
                        "items": []
                    },
                    "projects": {
                        "name": "Projects",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "projects",
                        "items": []
                    },
                    "publications": {
                        "name": "Publications",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "publications",
                        "items": []
                    },
                    "references": {
                        "name": "References",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "references",
                        "items": []
                    },
                    "skills": {
                        "name": "Skills",
                        "columns": 1,
                        "separateLinks": true,
                        "visible": true,
                        "id": "skills",
                        "items": [
                            {
                                "id": "skill-1",
                                "visible": true,
                                "name": "JavaScript",
                                "description": "Advanced proficiency in modern JavaScript",
                                "level": 5,
                                "keywords": ["ES6+", "TypeScript", "Node.js"]
                            }
                        ]
                    },
                    "custom": {}
                },
                "metadata": {
                    "template": "rhyhorn",
                    "layout": [
                        [
                            ["basics"],
                            ["summary", "experience", "education", "skills"]
                        ]
                    ],
                    "css": {
                        "value": "",
                        "visible": false
                    },
                    "page": {
                        "margin": 18,
                        "format": "a4",
                        "options": {
                            "breakLine": true,
                            "pageNumbers": true
                        }
                    },
                    "theme": {
                        "background": "#ffffff",
                        "text": "#000000",
                        "primary": "#dc2626"
                    },
                    "typography": {
                        "font": {
                            "family": "IBM Plex Sans",
                            "subset": "latin",
                            "variants": ["400", "600"],
                            "size": 14
                        },
                        "lineHeight": 1.5,
                        "hideIcons": false,
                        "underlineLinks": true
                    }
                }
            };
            
            localStorage.setItem('resume', JSON.stringify(sampleResume));
            alert('Test resume data has been set in localStorage!');
        }
        
        function clearData() {
            localStorage.removeItem('resume');
            alert('Resume data cleared from localStorage!');
        }
        
        function openArtboard() {
            window.open('http://localhost:6173/artboard/builder', '_blank');
        }
    </script>
</body>
</html>
