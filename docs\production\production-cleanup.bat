@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 生产环境清理
echo ========================================
echo.
echo ⚠️  警告: 此操作将删除所有数据！
echo    包括数据库、文件存储等所有内容
echo.
set /p confirm="确定要继续吗？(输入 YES 确认): "
if not "%confirm%"=="YES" (
    echo 操作已取消
    pause
    exit /b 0
)

:: 切换到项目目录
cd /d "%~dp0"

echo.
echo [1/4] 停止所有服务...
docker compose -f compose.production.yml down

echo.
echo [2/4] 删除 volumes...
docker compose -f compose.production.yml down -v

echo.
echo [3/4] 删除镜像...
docker compose -f compose.production.yml down --rmi all

echo.
echo [4/4] 清理未使用的资源...
docker system prune -f

echo.
echo ========================================
echo    清理完成！
echo ========================================
echo.
echo 💡 提示:
echo   - 所有数据已被删除
echo   - 下次启动将是全新环境
echo.
echo 🚀 重新开始:
echo   production-start.bat
echo.
pause
