@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 生产环境启动
echo ========================================
echo.

:: 切换到项目目录
cd /d "%~dp0"

echo [1/3] 检查 Docker 是否运行...
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未运行，请先启动 Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker 正在运行

echo.
echo [2/3] 构建并启动生产环境...
echo 这可能需要几分钟时间，请耐心等待...
docker compose -f compose.production.yml up -d --build

if errorlevel 1 (
    echo ❌ 启动失败
    pause
    exit /b 1
)

echo.
echo [3/3] 等待服务启动完成...
timeout /t 30 /nobreak >nul

echo.
echo ========================================
echo    启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo   主应用: http://localhost:3000
echo   MinIO 控制台: http://localhost:9001 (admin/admin)
echo.
echo 📊 检查服务状态:
echo   docker compose -f compose.production.yml ps
echo.
echo 🛑 停止服务:
echo   production-stop.bat
echo.
pause
