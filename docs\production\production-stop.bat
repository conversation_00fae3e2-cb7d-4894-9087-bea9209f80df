@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 生产环境停止
echo ========================================
echo.

:: 切换到项目目录
cd /d "%~dp0"

echo [1/2] 停止所有服务...
docker compose -f compose.production.yml down

if errorlevel 1 (
    echo ❌ 停止失败
    pause
    exit /b 1
)

echo.
echo [2/2] 检查服务状态...
docker compose -f compose.production.yml ps

echo.
echo ========================================
echo    停止完成！
echo ========================================
echo.
echo 💡 提示:
echo   - 数据已保存在 Docker volumes 中
echo   - 下次启动时数据会自动恢复
echo   - 如需完全清理，请运行: production-cleanup.bat
echo.
echo 🚀 重新启动:
echo   production-start.bat
echo.
pause
