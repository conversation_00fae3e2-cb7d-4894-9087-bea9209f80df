@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume Quick Start
echo ========================================
echo.
echo Choose an option:
echo   1. Start Project
echo   2. Stop Project
echo   3. Check Status
echo   4. Open Docs Folder
echo   5. View Usage Guide
echo   0. Exit
echo.
set /p choice=Enter option (0-5):

if "%choice%"=="1" (
    echo.
    echo Starting project...
    call docs\start-en.bat
) else if "%choice%"=="2" (
    echo.
    echo Stopping project...
    call docs\stop-en.bat
) else if "%choice%"=="3" (
    echo.
    echo Checking status...
    call docs\status-en.bat
) else if "%choice%"=="4" (
    echo.
    echo Opening docs folder...
    explorer docs
    pause
) else if "%choice%"=="5" (
    echo.
    echo Opening usage guide...
    start docs\USAGE_GUIDE.md
    pause
) else if "%choice%"=="0" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid option, please try again
    pause
    goto :eof
)

pause
