@echo off
chcp 65001 >nul
echo ========================================
echo    测试 Docker 镜像构建
echo ========================================
echo.

:: 切换到项目目录
cd /d "%~dp0"

echo [1/3] 检查 Docker 镜像...
docker images | findstr reactive-resume
if errorlevel 1 (
    echo ❌ 未找到 reactive-resume 镜像
    echo 请先运行构建: docker compose -f compose.production.yml build
    pause
    exit /b 1
) else (
    echo ✅ 找到 reactive-resume 镜像
)

echo.
echo [2/3] 检查镜像大小...
for /f "tokens=7" %%i in ('docker images reactive-resume* --format "table {{.Size}}"') do (
    echo 镜像大小: %%i
)

echo.
echo [3/3] 测试镜像启动...
echo 启动测试容器...
docker run --rm -d --name reactive-resume-test -p 3001:3000 reactive-resume_app:latest

if errorlevel 1 (
    echo ❌ 容器启动失败
    pause
    exit /b 1
)

echo 等待容器启动...
timeout /t 10 /nobreak >nul

echo 测试健康检查...
curl -s -o nul -w "HTTP状态: %%{http_code}\n" http://localhost:3001/api/health
if errorlevel 1 (
    echo ❌ 健康检查失败
) else (
    echo ✅ 健康检查通过
)

echo.
echo 停止测试容器...
docker stop reactive-resume-test

echo.
echo ========================================
echo    测试完成！
echo ========================================
echo.
echo 💡 如果测试通过，您可以使用:
echo   production-start.bat
echo.
pause
