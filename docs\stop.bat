@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 停止脚本
echo ========================================
echo.

:: 切换到项目目录
cd /d "c:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume"

:: 停止 Docker 服务
echo [1/2] 停止 Docker 服务...
docker compose -f compose.dev.yml down
if errorlevel 1 (
    echo 警告: Docker 服务停止可能有问题
) else (
    echo ✓ Docker 服务已停止
)

:: 检查端口占用
echo.
echo [2/2] 检查端口状态...
echo 检查端口 5173 (前端):
netstat -ano | findstr :5173
echo.
echo 检查端口 3000 (后端):
netstat -ano | findstr :3000
echo.
echo 检查端口 6173 (Artboard):
netstat -ano | findstr :6173
echo.

echo ========================================
echo    项目已停止
echo ========================================
echo.
echo 注意: 如果端口仍被占用，请手动结束相关进程
echo 或重启计算机以完全释放端口
echo.
pause
