@echo off
chcp 65001 >nul
echo ========================================
echo    Script Testing Tool
echo ========================================
echo.
echo Testing all scripts...
echo.

echo [1] Testing quick-start.bat...
if exist quick-start.bat (
    echo   OK - quick-start.bat exists
) else (
    echo   ERROR - quick-start.bat not found
)

echo.
echo [2] Testing docs scripts...
if exist docs\start-en.bat (
    echo   OK - start-en.bat exists
) else (
    echo   ERROR - start-en.bat not found
)

if exist docs\stop-en.bat (
    echo   OK - stop-en.bat exists
) else (
    echo   ERROR - stop-en.bat not found
)

if exist docs\status-en.bat (
    echo   OK - status-en.bat exists
) else (
    echo   ERROR - status-en.bat not found
)

echo.
echo [3] Testing syntax of English scripts...
echo Testing start-en.bat syntax...
call docs\start-en.bat --help >nul 2>&1
echo   start-en.bat syntax check completed

echo.
echo [4] Quick environment check...
echo Node.js version:
node --version 2>nul
if errorlevel 1 echo   Node.js not found

echo.
echo Docker status:
docker --version 2>nul
if errorlevel 1 echo   Docker not found

echo.
echo pnpm status:
pnpm --version 2>nul
if errorlevel 1 echo   pnpm not found

echo.
echo ========================================
echo Script testing completed
echo ========================================
echo.
echo Recommendations:
echo   - Use English versions (*-en.bat) for better compatibility
echo   - Chinese versions may have encoding issues on some systems
echo   - Use quick-start.bat for interactive menu
echo.
pause
