/// <reference types='vitest' />

import path from "node:path";
import { fileURLToPath } from "node:url";

import { nxViteTsPaths } from "@nx/vite/plugins/nx-tsconfig-paths.plugin";
import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vite";
import dts from "vite-plugin-dts";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  cacheDir: "../../node_modules/.vite/hooks",

  plugins: [
    react(),
    nxViteTsPaths(),
    // Temporarily disable DTS generation to fix build issues
    // dts({
    //   entryRoot: "src",
    //   tsconfigPath: path.join(__dirname, "tsconfig.lib.json"),
    //   exclude: ["**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx"],
    // }),
  ],

  build: {
    emptyOutDir: true,
    lib: {
      entry: "src/index.ts",
      name: "hooks",
      fileName: "index",
      formats: ["es", "cjs"],
    },
    rollupOptions: {
      external: [/^react.*/, "react-hook-form", "use-breakpoint", "usehooks-ts"],
    },
  },

  test: {
    globals: true,
    environment: "jsdom",
    include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
  },
});
