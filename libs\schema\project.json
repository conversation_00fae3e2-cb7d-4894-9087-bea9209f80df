{"name": "schema", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/schema/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/schema", "main": "libs/schema/src/index.ts", "tsConfig": "libs/schema/tsconfig.lib.json", "assets": ["libs/schema/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/schema/**/*.ts", "libs/schema/package.json"]}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/libs/schema"}}}, "tags": ["frontend", "backend"]}