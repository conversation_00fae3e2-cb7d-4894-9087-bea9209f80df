/// <reference types='vitest' />

import path from "node:path";
import { fileURLToPath } from "node:url";

import { nxViteTsPaths } from "@nx/vite/plugins/nx-tsconfig-paths.plugin";
import react from "@vitejs/plugin-react-swc";
import { defineConfig, searchForWorkspaceRoot } from "vite";
import dts from "vite-plugin-dts";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  cacheDir: "../../node_modules/.vite/ui",

  server: {
    fs: { allow: [searchForWorkspaceRoot(process.cwd())] },
  },

  plugins: [
    react(),
    nxViteTsPaths(),
    // Temporarily disable DTS generation to fix build issues
    // dts({
    //   entryRoot: "src",
    //   tsconfigPath: path.join(__dirname, "tsconfig.lib.json"),
    //   exclude: ["**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.stories.ts", "**/*.stories.tsx"],
    // }),
  ],

  build: {
    emptyOutDir: true,
    lib: {
      entry: "src/index.ts",
      name: "ui",
      fileName: "index",
      formats: ["es", "cjs"],
    },
    rollupOptions: {
      external: [/^react.*/, /^@radix-ui\/*/],
    },
  },

  test: {
    globals: true,
    environment: "jsdom",
    include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
  },
});
