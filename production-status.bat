@echo off
chcp 65001 >nul
echo ========================================
echo    Reactive Resume 生产环境状态
echo ========================================
echo.

:: 切换到项目目录
cd /d "%~dp0"

echo [1/4] Docker 服务状态...
docker compose -f compose.production.yml ps

echo.
echo [2/4] 健康检查...
echo 🔍 检查主应用...
curl -s -o nul -w "HTTP状态: %%{http_code}\n" http://localhost:3000/api/health
if errorlevel 1 (
    echo ❌ 主应用无法访问
) else (
    echo ✅ 主应用正常
)

echo.
echo 🔍 检查 MinIO...
curl -s -o nul -w "HTTP状态: %%{http_code}\n" http://localhost:9000/minio/health/live
if errorlevel 1 (
    echo ❌ MinIO 无法访问
) else (
    echo ✅ MinIO 正常
)

echo.
echo [3/4] 资源使用情况...
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" $(docker compose -f compose.production.yml ps -q) 2>nul

echo.
echo [4/4] 访问地址...
echo 🌐 主应用: http://localhost:3000
echo 🗄️  MinIO 控制台: http://localhost:9001
echo.

echo ========================================
echo    状态检查完成
echo ========================================
echo.
echo 💡 提示:
echo   - 如果服务未运行，请执行: production-start.bat
echo   - 查看详细日志: docker compose -f compose.production.yml logs
echo.
pause
