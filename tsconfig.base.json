{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "importHelpers": true, "strictNullChecks": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/artboard/*": ["apps/artboard/src/*"], "@/client/*": ["apps/client/src/*"], "@/server/*": ["apps/server/src/*"], "@reactive-resume/dto": ["libs/dto/src/index.ts"], "@reactive-resume/hooks": ["libs/hooks/src/index.ts"], "@reactive-resume/parser": ["libs/parser/src/index.ts"], "@reactive-resume/schema": ["libs/schema/src/index.ts"], "@reactive-resume/ui": ["libs/ui/src/index.ts"], "@reactive-resume/utils": ["libs/utils/src/index.ts"]}}, "exclude": ["node_modules", "dist", "tmp", ".nx"]}